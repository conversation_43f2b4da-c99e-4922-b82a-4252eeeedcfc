import React, { useEffect, useRef, useMemo } from 'react';
import { Message } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MoreVertical, 
  Reply, 
  Edit, 
  Trash2, 
  Pin, 
  Copy,
  Download,
  Eye,
  EyeOff
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { format, isToday, isYesterday } from 'date-fns';

interface ChatMessageListProps {
  messages: Message[];
  currentUserId: string;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  isLoading: boolean;
  onMessageAction?: (action: string, messageId: string) => void;
  onReplyToMessage?: (message: Message) => void;
}

/**
 * ChatMessageList Component
 * 
 * Displays a scrollable list of chat messages with role-based actions
 * and features like message reactions, editing, deletion, etc.
 */
export const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  currentUserId,
  userRole,
  features,
  theme,
  isLoading,
  onMessageAction,
  onReplyToMessage,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Group messages by date
  const groupedMessages = useMemo(() => {
    const groups: { date: string; messages: Message[] }[] = [];
    let currentDate = '';
    let currentGroup: Message[] = [];

    messages.forEach((message) => {
      const messageDate = format(new Date(message.created_at), 'yyyy-MM-dd');
      
      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({ date: currentDate, messages: currentGroup });
        }
        currentDate = messageDate;
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
    });

    if (currentGroup.length > 0) {
      groups.push({ date: currentDate, messages: currentGroup });
    }

    return groups;
  }, [messages]);

  // Format date for display
  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMMM d, yyyy');
  };

  // Check if user can perform action on message
  const canPerformAction = (action: string, message: Message) => {
    const isOwnMessage = message.user_id === currentUserId;
    
    switch (action) {
      case 'edit':
        return features.canEditMessages && isOwnMessage;
      case 'delete':
        return features.canDeleteMessages && (isOwnMessage || userRole === 'admin');
      case 'pin':
        return features.canPinMessages && (userRole === 'admin' || userRole === 'provider');
      case 'report':
        return features.canReportMessages && !isOwnMessage;
      default:
        return true;
    }
  };

  // Handle message action
  const handleMessageAction = (action: string, messageId: string) => {
    if (onMessageAction) {
      onMessageAction(action, messageId);
    }
  };

  // Render message actions menu
  const renderMessageActions = (message: Message) => {
    const isOwnMessage = message.user_id === currentUserId;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <MoreVertical className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          <DropdownMenuItem onClick={() => onReplyToMessage?.(message)}>
            <Reply className="h-3 w-3 mr-2" />
            Reply
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => navigator.clipboard.writeText(message.message)}>
            <Copy className="h-3 w-3 mr-2" />
            Copy
          </DropdownMenuItem>
          
          {canPerformAction('edit', message) && (
            <DropdownMenuItem onClick={() => handleMessageAction('edit', message.id)}>
              <Edit className="h-3 w-3 mr-2" />
              Edit
            </DropdownMenuItem>
          )}
          
          {canPerformAction('pin', message) && (
            <DropdownMenuItem onClick={() => handleMessageAction('pin', message.id)}>
              <Pin className="h-3 w-3 mr-2" />
              Pin
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          
          {canPerformAction('report', message) && (
            <DropdownMenuItem 
              className="text-orange-600"
              onClick={() => handleMessageAction('report', message.id)}
            >
              Report
            </DropdownMenuItem>
          )}
          
          {canPerformAction('delete', message) && (
            <DropdownMenuItem 
              className="text-red-600"
              onClick={() => handleMessageAction('delete', message.id)}
            >
              <Trash2 className="h-3 w-3 mr-2" />
              Delete
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render individual message
  const renderMessage = (message: Message) => {
    const isOwnMessage = message.user_id === currentUserId;
    const showAvatar = !isOwnMessage;
    const messageTime = format(new Date(message.created_at), 'HH:mm');

    return (
      <div
        key={message.id}
        className={cn(
          'group flex items-start space-x-2 px-4 py-2 hover:bg-[var(--chat-surface)] transition-colors',
          isOwnMessage && 'flex-row-reverse space-x-reverse'
        )}
      >
        {/* Avatar */}
        {showAvatar && (
          <Avatar className="w-8 h-8 flex-shrink-0">
            <AvatarImage src={message.user?.avatar || ''} />
            <AvatarFallback className="text-xs">
              {message.user?.name?.charAt(0)?.toUpperCase() || '?'}
            </AvatarFallback>
          </Avatar>
        )}
        
        {/* Message content */}
        <div className={cn(
          'flex-1 min-w-0',
          isOwnMessage && 'flex flex-col items-end'
        )}>
          {/* Message header */}
          {!isOwnMessage && (
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-sm font-medium text-[var(--chat-text)]">
                {message.user?.name || 'Unknown User'}
              </span>
              <Badge variant="outline" className="text-xs">
                {message.user?.type || 'user'}
              </Badge>
              <span className="text-xs text-[var(--chat-text-secondary)]">
                {messageTime}
              </span>
            </div>
          )}
          
          {/* Message bubble */}
          <div className={cn(
            'relative max-w-[70%] rounded-lg px-3 py-2 break-words',
            isOwnMessage 
              ? 'bg-[var(--chat-primary)] text-white ml-auto' 
              : 'bg-[var(--chat-surface)] text-[var(--chat-text)] border border-[var(--chat-border)]'
          )}>
            {/* Message text */}
            <div className="text-sm whitespace-pre-wrap">
              {message.message}
            </div>
            
            {/* Message metadata */}
            <div className={cn(
              'flex items-center justify-between mt-1 text-xs',
              isOwnMessage ? 'text-white/70' : 'text-[var(--chat-text-secondary)]'
            )}>
              {isOwnMessage && (
                <span>{messageTime}</span>
              )}
              
              {/* Read receipts */}
              {features.hasReadReceipts && isOwnMessage && (
                <div className="flex items-center space-x-1">
                  {message.read_at ? (
                    <Eye className="h-3 w-3" />
                  ) : (
                    <EyeOff className="h-3 w-3" />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Message actions */}
        <div className="flex-shrink-0">
          {renderMessageActions(message)}
        </div>
      </div>
    );
  };

  // Render loading state
  if (isLoading && messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-[var(--chat-text-secondary)]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--chat-primary)] mx-auto mb-2"></div>
          <p>Loading messages...</p>
        </div>
      </div>
    );
  }

  // Render empty state
  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-[var(--chat-text-secondary)]">
          <h3 className="text-lg font-medium mb-2">No messages yet</h3>
          <p>Start the conversation by sending a message</p>
        </div>
      </div>
    );
  }

  return (
    <ScrollArea className="flex-1" ref={scrollAreaRef}>
      <div className="space-y-4 pb-4">
        {groupedMessages.map((group) => (
          <div key={group.date}>
            {/* Date header */}
            <div className="flex justify-center py-2">
              <Badge variant="secondary" className="text-xs">
                {formatDateHeader(group.date)}
              </Badge>
            </div>
            
            {/* Messages for this date */}
            <div className="space-y-1">
              {group.messages.map(renderMessage)}
            </div>
          </div>
        ))}
        
        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>
    </ScrollArea>
  );
};

export default ChatMessageList;
